import { Application, Router } from "https://deno.land/x/oak@v12.6.1/mod.ts";
import { oakCors } from "https://deno.land/x/cors@v1.2.2/mod.ts";

const router = new Router();

// API Routes
router
  .get("/api/health", (ctx) => {
    ctx.response.body = { 
      status: "ok", 
      message: "Deno backend is running!",
      timestamp: new Date().toISOString()
    };
  })
  .get("/api/users", (ctx) => {
    // Mock data for demonstration
    ctx.response.body = {
      users: [
        { id: 1, name: "<PERSON>", email: "<EMAIL>" },
        { id: 2, name: "<PERSON>", email: "<EMAIL>" }
      ]
    };
  })
  .post("/api/users", async (ctx) => {
    const body = await ctx.request.body({ type: "json" }).value;
    
    // Mock response - in real app, you'd save to database
    ctx.response.body = {
      success: true,
      message: "User created successfully",
      user: { id: Date.now(), ...body }
    };
    ctx.response.status = 201;
  });

const app = new Application();

// Enable CORS for frontend communication
app.use(oakCors({
  origin: "http://localhost:5173", // Vue dev server default port
  credentials: true,
}));

// Logger middleware
app.use(async (ctx, next) => {
  console.log(`${ctx.request.method} ${ctx.request.url}`);
  await next();
});

// Use routes
app.use(router.routes());
app.use(router.allowedMethods());

const PORT = 8000;

console.log(`🦕 Deno server running on http://localhost:${PORT}`);
await app.listen({ port: PORT });
