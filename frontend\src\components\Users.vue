<template>
  <div class="users">
    <h2>User Management</h2>
    
    <!-- Add User Form -->
    <div class="add-user-form">
      <h3>Add New User</h3>
      <form @submit.prevent="addUser">
        <div class="form-group">
          <label for="name">Name:</label>
          <input 
            id="name"
            v-model="newUser.name" 
            type="text" 
            required 
            placeholder="Enter user name"
          />
        </div>
        
        <div class="form-group">
          <label for="email">Email:</label>
          <input 
            id="email"
            v-model="newUser.email" 
            type="email" 
            required 
            placeholder="Enter user email"
          />
        </div>
        
        <button type="submit" :disabled="loading" class="btn btn-primary">
          {{ loading ? 'Adding...' : 'Add User' }}
        </button>
      </form>
    </div>
    
    <!-- Users List -->
    <div class="users-list">
      <h3>Users List</h3>
      <button @click="fetchUsers" :disabled="loading" class="btn btn-secondary">
        {{ loading ? 'Loading...' : 'Refresh Users' }}
      </button>
      
      <div v-if="users.length > 0" class="users-grid">
        <div 
          v-for="user in users" 
          :key="user.id" 
          class="user-card"
        >
          <h4>{{ user.name }}</h4>
          <p>{{ user.email }}</p>
          <small>ID: {{ user.id }}</small>
        </div>
      </div>
      
      <div v-else-if="!loading" class="no-users">
        <p>No users found. Click "Refresh Users" to load from the backend.</p>
      </div>
    </div>
    
    <!-- Status Messages -->
    <div v-if="message" class="message" :class="messageType">
      {{ message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import axios from 'axios'

// Types
interface User {
  id: number
  name: string
  email: string
}

// Reactive state
const users = ref<User[]>([])
const loading = ref(false)
const message = ref('')
const messageType = ref<'success' | 'error'>('success')

const newUser = ref({
  name: '',
  email: ''
})

// Methods
const fetchUsers = async () => {
  loading.value = true
  message.value = ''
  
  try {
    const response = await axios.get('/api/users')
    users.value = response.data.users
    showMessage('Users loaded successfully!', 'success')
  } catch (error) {
    showMessage('Failed to load users. Make sure the backend is running.', 'error')
    console.error('Error fetching users:', error)
  } finally {
    loading.value = false
  }
}

const addUser = async () => {
  if (!newUser.value.name || !newUser.value.email) {
    showMessage('Please fill in all fields', 'error')
    return
  }
  
  loading.value = true
  message.value = ''
  
  try {
    const response = await axios.post('/api/users', newUser.value)
    
    // Add the new user to the list
    users.value.push(response.data.user)
    
    // Reset form
    newUser.value = { name: '', email: '' }
    
    showMessage('User added successfully!', 'success')
  } catch (error) {
    showMessage('Failed to add user. Please try again.', 'error')
    console.error('Error adding user:', error)
  } finally {
    loading.value = false
  }
}

const showMessage = (text: string, type: 'success' | 'error') => {
  message.value = text
  messageType.value = type
  
  // Clear message after 3 seconds
  setTimeout(() => {
    message.value = ''
  }, 3000)
}

// Load users when component mounts
onMounted(() => {
  fetchUsers()
})
</script>

<style scoped>
.users {
  max-width: 1000px;
}

h2 {
  color: #2c3e50;
  margin-bottom: 2rem;
}

.add-user-form {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.add-user-form h3 {
  margin-top: 0;
  color: #34495e;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #555;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.users-list {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.users-list h3 {
  margin-top: 0;
  color: #34495e;
}

.users-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.user-card {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.user-card h4 {
  margin: 0 0 0.5rem 0;
  color: #2c3e50;
}

.user-card p {
  margin: 0 0 0.5rem 0;
  color: #666;
}

.user-card small {
  color: #999;
}

.no-users {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.btn {
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.btn-primary {
  background-color: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn-secondary {
  background-color: #95a5a6;
  color: white;
  margin-bottom: 1rem;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #7f8c8d;
}

.btn:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.message {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 4px;
  font-weight: 500;
}

.message.success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.message.error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}
</style>
