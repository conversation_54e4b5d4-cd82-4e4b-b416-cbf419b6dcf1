# Vue 3 + Deno Full-Stack Application

A modern full-stack application built with Vue 3 (Composition API) frontend and Deno backend.

## 🚀 Tech Stack

### Frontend
- **Vue 3** with Composition API
- **TypeScript** for type safety
- **Vite** for fast development and building
- **Vue Router** for client-side routing
- **Axios** for HTTP requests

### Backend
- **Deno** runtime environment
- **Oak** framework for HTTP server
- **TypeScript** for type safety
- **CORS** enabled for frontend communication

## 📁 Project Structure

```
├── backend/
│   ├── main.ts          # Deno server entry point
│   └── deno.json        # Deno configuration
├── frontend/
│   ├── src/
│   │   ├── components/  # Vue components
│   │   ├── App.vue      # Root component
│   │   └── main.ts      # Vue app entry point
│   ├── index.html       # HTML template
│   ├── package.json     # Node dependencies
│   ├── vite.config.ts   # Vite configuration
│   └── tsconfig.json    # TypeScript configuration
└── README.md
```

## 🛠️ Getting Started

### Prerequisites
- [Deno](https://deno.land/) (v1.37+)
- [Node.js](https://nodejs.org/) (v18+)
- [npm](https://www.npmjs.com/) or [yarn](https://yarnpkg.com/)

### Installation & Setup

1. **Clone and navigate to the project:**
   ```bash
   cd vue+deno
   ```

2. **Install frontend dependencies:**
   ```bash
   cd frontend
   npm install
   ```

3. **Start the backend server:**
   ```bash
   cd ../backend
   deno task dev
   ```
   The Deno server will start on `http://localhost:8000`

4. **Start the frontend development server:**
   ```bash
   cd ../frontend
   npm run dev
   ```
   The Vue app will start on `http://localhost:5173`

## 🔧 Available Scripts

### Backend (Deno)
- `deno task dev` - Start development server with file watching
- `deno task start` - Start production server

### Frontend (Vue)
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build

## 🌐 API Endpoints

The backend provides the following REST API endpoints:

- `GET /api/health` - Health check endpoint
- `GET /api/users` - Get all users
- `POST /api/users` - Create a new user

## 🎯 Features

- ✅ Vue 3 with Composition API
- ✅ TypeScript support for both frontend and backend
- ✅ Hot reload for development
- ✅ CORS configured for cross-origin requests
- ✅ Proxy setup for API calls
- ✅ Responsive design
- ✅ Error handling
- ✅ Loading states
- ✅ Form validation

## 🔄 Development Workflow

1. Both servers need to be running for full functionality
2. The frontend dev server proxies API calls to the backend
3. Changes to Vue files trigger hot reload
4. Changes to Deno files restart the server automatically

## 📝 Notes

- The backend uses mock data for demonstration
- CORS is configured to allow requests from the Vue dev server
- The frontend uses Axios for HTTP requests with proper error handling
- All components use Vue 3 Composition API with `<script setup>` syntax

## 🚀 Next Steps

Consider adding:
- Database integration (PostgreSQL, MongoDB, etc.)
- Authentication & authorization
- State management (Pinia)
- Testing (Vitest, Deno test)
- Docker containerization
- CI/CD pipeline
