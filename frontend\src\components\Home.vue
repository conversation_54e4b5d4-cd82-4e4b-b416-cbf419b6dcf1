<template>
  <div class="home">
    <h2>Welcome to Vue + Deno App</h2>
    <p>This is a modern full-stack application built with:</p>
    
    <div class="tech-stack">
      <div class="tech-item">
        <h3>Frontend</h3>
        <ul>
          <li>Vue 3 with Composition API</li>
          <li>TypeScript</li>
          <li>Vite</li>
          <li>Vue Router</li>
        </ul>
      </div>
      
      <div class="tech-item">
        <h3>Backend</h3>
        <ul>
          <li>Deno Runtime</li>
          <li>Oak Framework</li>
          <li>TypeScript</li>
          <li>REST API</li>
        </ul>
      </div>
    </div>
    
    <div class="api-status">
      <h3>Backend Status</h3>
      <button @click="checkHealth" :disabled="loading" class="btn">
        {{ loading ? 'Checking...' : 'Check Backend Health' }}
      </button>
      
      <div v-if="healthStatus" class="status-result" :class="healthStatus.success ? 'success' : 'error'">
        <p><strong>Status:</strong> {{ healthStatus.message }}</p>
        <p v-if="healthStatus.timestamp"><strong>Time:</strong> {{ healthStatus.timestamp }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import axios from 'axios'

// Reactive state using Composition API
const loading = ref(false)
const healthStatus = ref<{
  success: boolean
  message: string
  timestamp?: string
} | null>(null)

// Methods
const checkHealth = async () => {
  loading.value = true
  healthStatus.value = null
  
  try {
    const response = await axios.get('/api/health')
    healthStatus.value = {
      success: true,
      message: response.data.message,
      timestamp: response.data.timestamp
    }
  } catch (error) {
    healthStatus.value = {
      success: false,
      message: 'Backend is not responding. Make sure the Deno server is running.'
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.home {
  max-width: 800px;
}

h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.tech-stack {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 2rem 0;
}

.tech-item {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tech-item h3 {
  color: #34495e;
  margin-top: 0;
}

.tech-item ul {
  list-style-type: none;
  padding: 0;
}

.tech-item li {
  padding: 0.25rem 0;
  border-left: 3px solid #3498db;
  padding-left: 1rem;
  margin: 0.5rem 0;
}

.api-status {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-top: 2rem;
}

.btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.btn:hover:not(:disabled) {
  background-color: #2980b9;
}

.btn:disabled {
  background-color: #bdc3c7;
  cursor: not-allowed;
}

.status-result {
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 4px;
}

.status-result.success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.status-result.error {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}
</style>
